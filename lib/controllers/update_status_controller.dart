import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/order_model.dart';

class UpdateStatusController extends GetxController {
  // Observable variables
  final RxInt selectedNavIndex = 2.obs; // Orders List is index 2
  final RxBool isLoading = false.obs;
  final RxString selectedOrderStatus = 'Pouching'.obs;
  final Rx<OrderModel?> currentOrder = Rx<OrderModel?>(null);

  // Controllers
  final TextEditingController remarksController = TextEditingController();

  // Order status options
  final List<String> orderStatusOptions = [
    'On-boarding',
    'Designing',
    'Sampling',
    'Design Plate Approved',
    'Cylinder Development',
    'Polyester Sample Approved',
    'Polyester Printing',
    'Lamination',
    'Metallised Pasting',
    'Heating',
    'Curing',
    'Zipper Addition',
    'Slitting',
    'Pouching',
    'Sorting',
    'Packing',
    'Ready to Dispatch',
    'Dispatched'
  ];

  // Status progress data
  final List<Map<String, dynamic>> statusProgress = [
    {
      'title': 'On-boarding',
      'date': '4 June 2025',
      'time': '9:30 Pm',
      'isCompleted': true,
      'icon': 'check_circle',
    },
    {
      'title': 'Designing',
      'date': '4 June 2025',
      'time': '10:30 Am',
      'isCompleted': true,
      'icon': 'design_services',
    },
    {
      'title': 'Sampling',
      'date': '4 June 2025',
      'time': '11:30 Am',
      'isCompleted': true,
      'icon': 'science',
    },
    {
      'title': 'Design plate Approved',
      'date': '4 June 2025',
      'time': '11:30 Am',
      'isCompleted': true,
      'icon': 'verified',
    },
  ];

  @override
  void onInit() {
    super.onInit();
    
    // Get order data from route arguments
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null && arguments['order'] != null) {
      currentOrder.value = arguments['order'] as OrderModel;
      selectedOrderStatus.value = currentOrder.value?.status ?? 'Pouching';
    }

    // Initialize remarks with sample text
    remarksController.text = 'Paper trimmed to required size, edges are clean';
  }

  @override
  void onClose() {
    remarksController.dispose();
    super.onClose();
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.offNamed('/customers-list');
        break;
      case 2:
        // Orders List
        Get.offNamed('/order-list');
        break;
    }
  }

  // Handle back navigation
  void onBackPressed() {
    Get.back();
  }

  // Handle order status change
  void onOrderStatusChanged(String? newStatus) {
    if (newStatus != null) {
      selectedOrderStatus.value = newStatus;
    }
  }

  // Handle update order status
  void onUpdateOrderStatus() {
    if (currentOrder.value == null) {
      Get.snackbar(
        'Error',
        'No order data found',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    isLoading.value = true;

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      isLoading.value = false;
      
      Get.snackbar(
        'Success',
        'Order status updated successfully to ${selectedOrderStatus.value}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Navigate back after successful update
      Get.back();
    });
  }

  // Format date for display
  String formatDate(DateTime date) {
    return '${_getMonthName(date.month)} ${date.day}, ${date.year}';
  }

  String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  // Get status color
  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pouching':
        return const Color(0xFF8B5CF6); // Purple
      case 'packing':
        return const Color(0xFF3B82F6); // Blue
      case 'design plate approved':
        return const Color(0xFF10B981); // Green
      case 'sorting':
        return const Color(0xFFEC4899); // Pink
      case 'cylinder development':
        return const Color(0xFFF59E0B); // Orange
      case 'heating':
        return const Color(0xFFEF4444); // Red
      case 'on-boarding':
        return const Color(0xFF06B6D4); // Cyan
      case 'ready to dispatch':
        return const Color(0xFF84CC16); // Lime
      default:
        return const Color(0xFF6B7280); // Gray
    }
  }
}
