import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/firebase_service.dart';
import '../routes/app_routes.dart';
import 'login_screen.dart';
import 'dashboard_screen.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return GetX<FirebaseService>(
      builder: (firebaseService) {
        // Show loading while checking auth state
        if (firebaseService.user == null && firebaseService.isLoading.value) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        // If user is signed in, show dashboard
        if (firebaseService.isSignedIn) {
          // Navigate to dashboard if not already there
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (Get.currentRoute != AppRoutes.dashboard) {
              Get.offAllNamed(AppRoutes.dashboard);
            }
          });
          return const DashboardScreen();
        }
        
        // If user is not signed in, show login screen
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (Get.currentRoute != AppRoutes.login) {
            Get.offAllNamed(AppRoutes.login);
          }
        });
        return const LoginScreen();
      },
    );
  }
}
